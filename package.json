{"name": "hirevia", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.49.0", "@clerk/themes": "^2.4.19", "@gsap/react": "^2.1.2", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.57.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.13", "@uiw/react-md-editor": "^3.14.0", "@vitejs/plugin-react": "^5.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "lucide-react": "^0.544.0", "motion": "^12.23.22", "ogl": "^1.0.11", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-router-dom": "^7.9.1", "react-spinners": "^0.17.0", "router-dom": "^3.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "vaul": "^1.1.2", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.8", "vite": "^7.1.2"}}