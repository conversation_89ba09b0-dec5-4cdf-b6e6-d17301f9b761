import Header from "@/components/Header";
import LightRays from "@/components/LightRays";
import React from "react";
import { Outlet } from "react-router-dom";
// import './App.css'

const AppLayout = () => {
  return (
    <>
      <div className="grid-background"></div>
      {/* <div
      className="h-screen w-screen absolute top-0 left-0 fixed">
        <LightRays
          raysOrigin="top-center"
          raysColor="#00ffff"
          raysSpeed={1.5}
          lightSpread={0.8}
          rayLength={1.2}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.1}
          distortion={0.05}
          className="custom-rays"
        />
      </div> */}
      <main className="min-h-screen container">
        <Header />
        <Outlet />
      </main>
      <div className="p-8 text-2xl text-center">
        Made With 💗 By DeekshantTyagi.
      </div>
    </>
  );
};

export default AppLayout;
